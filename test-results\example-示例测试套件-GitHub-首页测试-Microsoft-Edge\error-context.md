# Test info

- Name: 示例测试套件 >> GitHub 首页测试
- Location: C:\Users\<USER>\Desktop\test\tests\example.spec.js:31:3

# Error details

```
Error: browserType.launch: Chromium distribution 'msedge' is not found at C:\Users\<USER>\AppData\Local\Microsoft\Edge\Application\msedge.exe
Run "npx playwright install msedge"
```

# Test source

```ts
   1 | // @ts-check
   2 | const { test, expect } = require('@playwright/test');
   3 |
   4 | test.describe('示例测试套件', () => {
   5 |   test('基本页面导航测试', async ({ page }) => {
   6 |     // 访问百度首页
   7 |     await page.goto('https://www.baidu.com');
   8 |     
   9 |     // 验证页面标题
   10 |     await expect(page).toHaveTitle(/百度/);
   11 |     
   12 |     // 验证搜索框存在
   13 |     const searchBox = page.locator('#kw');
   14 |     await expect(searchBox).toBeVisible();
   15 |     
   16 |     // 输入搜索内容
   17 |     await searchBox.fill('Playwright 自动化测试');
   18 |     
   19 |     // 点击搜索按钮
   20 |     await page.click('#su');
   21 |     
   22 |     // 等待搜索结果页面加载
   23 |     await page.waitForLoadState('networkidle');
   24 |     
   25 |     // 验证搜索结果页面
   26 |     await expect(page.locator('#content_left')).toBeVisible();
   27 |     
   28 |     console.log('✅ 百度搜索测试完成');
   29 |   });
   30 |
>  31 |   test('GitHub 首页测试', async ({ page }) => {
      |   ^ Error: browserType.launch: Chromium distribution 'msedge' is not found at C:\Users\<USER>\AppData\Local\Microsoft\Edge\Application\msedge.exe
   32 |     // 访问GitHub首页
   33 |     await page.goto('https://github.com');
   34 |     
   35 |     // 验证页面标题包含GitHub
   36 |     await expect(page).toHaveTitle(/GitHub/);
   37 |     
   38 |     // 验证登录按钮存在
   39 |     const signInButton = page.locator('a[href="/login"]').first();
   40 |     await expect(signInButton).toBeVisible();
   41 |     
   42 |     // 验证搜索框存在
   43 |     const searchButton = page.locator('[data-target="qbsearch-input.inputButton"]').first();
   44 |     await expect(searchButton).toBeVisible();
   45 |     
   46 |     console.log('✅ GitHub 首页测试完成');
   47 |   });
   48 |
   49 |   test('表单交互测试', async ({ page }) => {
   50 |     // 创建一个简单的HTML页面进行测试
   51 |     const htmlContent = `
   52 |       <!DOCTYPE html>
   53 |       <html>
   54 |       <head>
   55 |         <title>测试表单</title>
   56 |         <meta charset="utf-8">
   57 |       </head>
   58 |       <body>
   59 |         <h1>测试表单</h1>
   60 |         <form id="testForm">
   61 |           <input type="text" id="name" placeholder="姓名" required>
   62 |           <input type="email" id="email" placeholder="邮箱" required>
   63 |           <select id="city">
   64 |             <option value="">选择城市</option>
   65 |             <option value="beijing">北京</option>
   66 |             <option value="shanghai">上海</option>
   67 |             <option value="guangzhou">广州</option>
   68 |           </select>
   69 |           <input type="checkbox" id="agree"> 同意条款
   70 |           <button type="submit">提交</button>
   71 |         </form>
   72 |         <div id="result"></div>
   73 |         <script>
   74 |           document.getElementById('testForm').addEventListener('submit', function(e) {
   75 |             e.preventDefault();
   76 |             document.getElementById('result').textContent = '表单提交成功！';
   77 |           });
   78 |         </script>
   79 |       </body>
   80 |       </html>
   81 |     `;
   82 |     
   83 |     // 使用data URL加载HTML内容
   84 |     await page.goto(`data:text/html,${encodeURIComponent(htmlContent)}`);
   85 |     
   86 |     // 填写表单
   87 |     await page.fill('#name', '张三');
   88 |     await page.fill('#email', '<EMAIL>');
   89 |     await page.selectOption('#city', 'beijing');
   90 |     await page.check('#agree');
   91 |     
   92 |     // 提交表单
   93 |     await page.click('button[type="submit"]');
   94 |     
   95 |     // 验证结果
   96 |     await expect(page.locator('#result')).toHaveText('表单提交成功！');
   97 |     
   98 |     console.log('✅ 表单交互测试完成');
   99 |   });
  100 | });
  101 |
```