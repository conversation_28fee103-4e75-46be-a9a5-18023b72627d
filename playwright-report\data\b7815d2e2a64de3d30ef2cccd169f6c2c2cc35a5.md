# Test info

- Name: 示例测试套件 >> 基本页面导航测试
- Location: C:\Users\<USER>\Desktop\test\tests\example.spec.js:5:3

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

Locator: locator('#kw')
Expected: visible
Received: hidden
Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for locator('#kw')
    9 × locator resolved to <textarea id="kw" name="word" maxlength="64" autocorrect="off" autocomplete="off" class="se-input adjust-input"></textarea>
      - unexpected value "hidden"

    at C:\Users\<USER>\Desktop\test\tests\example.spec.js:14:29
```

# Page snapshot

```yaml
- img
- link:
  - /url: https://m.baidu.com/l=1/tc?logid=9528268918482368951&from=844b&ref=index_iphone&nsrc=mMQ82pJog0pERasoA4JVBP3d7Jw4Qw4k2o9G6gNVYpi0bZpZV%2FVZQOT1gaAq1PDXJEVhEfQ%2Bdk%2B8ALLultP%2ByKi19VCLNUC5uyDX9U%2FasiI%3D&bdver=2_1&bdenc=1&ct=10&cst=2&logFrom=navs_square
- img "百度一下,你就知道"
- searchbox
- button "Submit"
- button "百度一下"
- img "head_icon"
- text: 登录
- list:
  - listitem:
    - link "我的关注":
      - /url: https://wappass.baidu.com/passport/?login&tpl=wise&subpro=wise&sms=1&regtype=1&u=https%3A%2F%2Fmbd.baidu.com%2Fwebpage%3Faction%3Dicard%26type%3Dsubscribe%26channel%3Dwise_home
  - listitem:
    - link "我的收藏":
      - /url: https://wappass.baidu.com/passport/?login&tpl=wise&subpro=wise&sms=1&regtype=1&u=http%3A%2F%2Fmysearch.pae.baidu.com%2Fpage%2Ffav%3Faction%3Dr
  - listitem:
    - link "皮肤中心":
      - /url: https://wappass.baidu.com/passport/?login&tpl=wise&subpro=wise&sms=1&regtype=1&u=https%3A%2F%2Fm.baidu.com%2F%3F%23iview%3Dskin
  - listitem:
    - link "用户反馈":
      - /url: http://ufosdk.baidu.com/?m=Client&a=commonsubmit&appid=517&activityId=1&needEmail=false&bw=4E4670D693EBB4F4A983124C6D8D5207&kw=&tn=iphone&pageid=6
- text: 设置 ©2025 Baidu 使用百度前必读
- link "用户反馈":
  - /url: http://ufosdk.baidu.com/?m=Client&a=commonsubmit&appid=517&activityId=1&needEmail=false&bw=A9A3A894ABB2A3374235A2C928627468&kw=&tn=iphone&pageid=6
- link "使用百度前必读 Baidu 京ICP证030173号":
  - /url: https://m.baidu.com/sf?pd=sd_privacy_terms&ms=1&ms=1&word=%E5%85%8D%E8%B4%A3%E5%A3%B0%E6%98%8E&title=%E5%85%8D%E8%B4%A3%E5%A3%B0%E6%98%8E&openapi=1&from_sf=1&resource_id=37483&dsp=iphone&tn=wisexmlnew&ext=%7B%22pid%22%3A%22mianze-shengming%22%7D&lid=&referlid=9224097818218589594&frsrcid=37483&frorder=1
- link "京公网安备11000002000001号":
  - /url: http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11000002000001
```

# Test source

```ts
   1 | // @ts-check
   2 | const { test, expect } = require('@playwright/test');
   3 |
   4 | test.describe('示例测试套件', () => {
   5 |   test('基本页面导航测试', async ({ page }) => {
   6 |     // 访问百度首页
   7 |     await page.goto('https://www.baidu.com');
   8 |     
   9 |     // 验证页面标题
   10 |     await expect(page).toHaveTitle(/百度/);
   11 |     
   12 |     // 验证搜索框存在
   13 |     const searchBox = page.locator('#kw');
>  14 |     await expect(searchBox).toBeVisible();
      |                             ^ Error: Timed out 5000ms waiting for expect(locator).toBeVisible()
   15 |     
   16 |     // 输入搜索内容
   17 |     await searchBox.fill('Playwright 自动化测试');
   18 |     
   19 |     // 点击搜索按钮
   20 |     await page.click('#su');
   21 |     
   22 |     // 等待搜索结果页面加载
   23 |     await page.waitForLoadState('networkidle');
   24 |     
   25 |     // 验证搜索结果页面
   26 |     await expect(page.locator('#content_left')).toBeVisible();
   27 |     
   28 |     console.log('✅ 百度搜索测试完成');
   29 |   });
   30 |
   31 |   test('GitHub 首页测试', async ({ page }) => {
   32 |     // 访问GitHub首页
   33 |     await page.goto('https://github.com');
   34 |     
   35 |     // 验证页面标题包含GitHub
   36 |     await expect(page).toHaveTitle(/GitHub/);
   37 |     
   38 |     // 验证登录按钮存在
   39 |     const signInButton = page.locator('a[href="/login"]').first();
   40 |     await expect(signInButton).toBeVisible();
   41 |     
   42 |     // 验证搜索框存在
   43 |     const searchButton = page.locator('[data-target="qbsearch-input.inputButton"]').first();
   44 |     await expect(searchButton).toBeVisible();
   45 |     
   46 |     console.log('✅ GitHub 首页测试完成');
   47 |   });
   48 |
   49 |   test('表单交互测试', async ({ page }) => {
   50 |     // 创建一个简单的HTML页面进行测试
   51 |     const htmlContent = `
   52 |       <!DOCTYPE html>
   53 |       <html>
   54 |       <head>
   55 |         <title>测试表单</title>
   56 |         <meta charset="utf-8">
   57 |       </head>
   58 |       <body>
   59 |         <h1>测试表单</h1>
   60 |         <form id="testForm">
   61 |           <input type="text" id="name" placeholder="姓名" required>
   62 |           <input type="email" id="email" placeholder="邮箱" required>
   63 |           <select id="city">
   64 |             <option value="">选择城市</option>
   65 |             <option value="beijing">北京</option>
   66 |             <option value="shanghai">上海</option>
   67 |             <option value="guangzhou">广州</option>
   68 |           </select>
   69 |           <input type="checkbox" id="agree"> 同意条款
   70 |           <button type="submit">提交</button>
   71 |         </form>
   72 |         <div id="result"></div>
   73 |         <script>
   74 |           document.getElementById('testForm').addEventListener('submit', function(e) {
   75 |             e.preventDefault();
   76 |             document.getElementById('result').textContent = '表单提交成功！';
   77 |           });
   78 |         </script>
   79 |       </body>
   80 |       </html>
   81 |     `;
   82 |     
   83 |     // 使用data URL加载HTML内容
   84 |     await page.goto(`data:text/html,${encodeURIComponent(htmlContent)}`);
   85 |     
   86 |     // 填写表单
   87 |     await page.fill('#name', '张三');
   88 |     await page.fill('#email', '<EMAIL>');
   89 |     await page.selectOption('#city', 'beijing');
   90 |     await page.check('#agree');
   91 |     
   92 |     // 提交表单
   93 |     await page.click('button[type="submit"]');
   94 |     
   95 |     // 验证结果
   96 |     await expect(page.locator('#result')).toHaveText('表单提交成功！');
   97 |     
   98 |     console.log('✅ 表单交互测试完成');
   99 |   });
  100 | });
  101 |
```