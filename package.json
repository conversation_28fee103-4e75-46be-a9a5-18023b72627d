{"name": "test", "version": "1.0.0", "main": "index.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:ui": "playwright test --ui", "test:report": "playwright show-report", "test:chromium": "playwright test --project=chromium", "test:firefox": "playwright test --project=firefox", "test:webkit": "playwright test --project=webkit"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@playwright/test": "^1.52.0"}}