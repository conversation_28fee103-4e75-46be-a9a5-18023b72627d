# Test info

- Name: 示例测试套件 >> GitHub 首页测试
- Location: C:\Users\<USER>\Desktop\test\tests\example.spec.js:31:3

# Error details

```
Error: page.goto: Test timeout of 30000ms exceeded.
Call log:
  - navigating to "https://github.com/", waiting until "load"

    at C:\Users\<USER>\Desktop\test\tests\example.spec.js:33:16
```

# Page snapshot

```yaml
- link "Skip to content":
  - /url: "#start-of-content"
- alert "Announcement":
  - text: Discover AI-driven development insights at GitHub Galaxy ‘25.
  - link "Learn more":
    - /url: https://galaxy.github.com/?utm_source=github&utm_medium=site-banner&utm_campaign=2025q4-evt-ww-virtual-galaxy&ref_page=home
  - button "Close"
- banner:
  - heading "Navigation Menu" [level=2]
  - link "Homepage":
    - /url: /
  - navigation "Global":
    - list:
      - listitem:
        - button "Product"
      - listitem:
        - button "Solutions"
      - listitem:
        - button "Resources"
      - listitem:
        - button "Open Source"
      - listitem:
        - button "Enterprise"
      - listitem:
        - link "Pricing":
          - /url: https://github.com/pricing
  - button "Search or jump to…"
  - link "Sign in":
    - /url: /login
  - link "Sign up":
    - /url: /signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F&source=header-home
- main:
  - region "Build and ship software on a single, collaborative platform":
    - heading "Build and ship software on a single, collaborative platform" [level=1]
    - paragraph: Join the world’s most widely adopted AI-powered developer platform.
  - form "Create your GitHub account":
    - text: Enter your email
    - textbox "Enter your email"
    - button "Sign up for GitHub"
  - link "Try GitHub Copilot":
    - /url: /github-copilot/pro?cft=copilot_lo.copilot_plans.cfi
  - heading "GitHub features" [level=2]
  - button "Pause demo"
  - text: A demonstration animation of a code editor using GitHub Copilot Chat, where the user requests GitHub Copilot to refactor duplicated logic and extract it into a reusable function for a given code snippet.
  - tablist:
    - tab "Code"
    - tab "Plan"
    - tab "Collaborate"
    - tab "Automate"
    - tab "Secure"
  - region: Build code quickly and more securely with GitHub Copilot embedded throughout your workflows.
  - heading "GitHub is used by" [level=2]
  - img "Shopify"
  - img "EY"
  - img "Figma"
  - img "Duolingo"
  - img "New York Times"
  - img "Mercado Libre"
  - img "American Airlines"
  - img "Ford"
  - img "Mercedes Benz"
  - img "Société Générale"
  - img "Vodafone"
  - img "Philips"
  - img "SAP"
  - img "Infosys"
  - img "Spotify"
  - button "Logo suite animation is currently playing. Click to pause.": Pause
  - heading "Accelerate performance" [level=2]
  - paragraph: With GitHub Copilot embedded throughout the platform, you can simplify your toolchain, automate tasks, and improve the developer experience.
  - button "Pause video"
  - text: "A Copilot chat window with extensions enabled. The user inputs the @ symbol to reveal a list of five Copilot Extensions. @Sentry is selected from the list, which shifts the window to a chat directly with that extension. There are three sample prompts at the bottom of the chat window, allowing the user to Get incident information, Edit status on incident, or List the latest issues. The last one is activated to send the prompt: @Sentry List the latest issues. The extension then lists several new issues and their metadata."
  - heading "Work 55% faster. Jump to footnote 1 Increase productivity with AI-powered coding assistance, including code completion, chat, and more." [level=3]:
    - text: Work 55% faster.
    - link "Jump to footnote 1":
      - /url: "#footnote-1"
      - superscript: Jump to footnote 1
    - text: Increase productivity with AI-powered coding assistance, including code completion, chat, and more.
  - link "Explore GitHub Copilot":
    - /url: /features/copilot
  - paragraph: Duolingo boosts developer speed by 25% with GitHub Copilot
  - link "Read customer story":
    - /url: /customer-stories/duolingo
  - paragraph: 2024 Gartner® Magic Quadrant™ for AI Code Assistants
  - link "Read report":
    - /url: https://www.gartner.com/doc/reprints?id=1-2IKO4MPE&ct=240819&st=sb
  - term:
    - button "Automate any workflow" [expanded]:
      - heading "Automate any workflow" [level=3]
  - definition:
    - paragraph: Optimize your process with simple and secured CI/CD.
    - text: A list of workflows displays a heading ‘45,167 workflow runs’ at the top. Below are five rows of completed workflows accompanied by their completion time and their duration formatted in minutes and seconds.
    - link "Discover GitHub Actions":
      - /url: /features/actions
  - term:
    - button "Get up and running in seconds":
      - heading "Get up and running in seconds" [level=3]
  - term:
    - button "Build on the go":
      - heading "Build on the go" [level=3]
  - term:
    - button "Integrate the tools you love":
      - heading "Integrate the tools you love" [level=3]
  - heading "Built-in application security where found means fixed" [level=2]
  - paragraph: Use AI to find and fix vulnerabilities—freeing your teams to ship more secure software faster.
  - heading "Apply fixes in seconds. Spend less time fixing vulnerabilities and more time building features with Copilot Autofix." [level=3]
  - link "Explore GitHub Advanced Security":
    - /url: /security/advanced-security
  - img "Copilot Autofix identifies vulnerable code and provides an explanation, together with a secure code suggestion to remediate the vulnerability."
  - paragraph: Solve security debt. Leverage AI-assisted security campaigns to reduce application vulnerabilities and zero-day attacks.
  - link "Discover security campaigns":
    - /url: /security/advanced-security
  - img "A security campaign screen displays the campaign’s progress bar with 97% completed of 701 alerts. A total of 23 alerts are left with 13 in progress, and the campaign started 20 days ago. The status below shows that there are 7 days left in the campaign with a due date of November 15, 2024."
  - paragraph: Dependencies you can depend on. Update vulnerable dependencies with supported fixes for breaking changes.
  - link "Learn about Dependabot":
    - /url: /security/advanced-security/software-supply-chain
  - img "List of dependencies defined in a requirements .txt file."
  - paragraph: "Your secrets, your business: protected. Detect, prevent, and remediate leaked secrets across your organization."
  - link "Read about secret scanning":
    - /url: /security/advanced-security/secret-protection
  - img "GitHub push protection confirms and displays an active secret, and blocks the push."
  - paragraph:
    - text: 7x faster vulnerability fixes with GitHub
    - link "Jump to footnote 2":
      - /url: "#footnote-2"
      - superscript: Jump to footnote 2
  - paragraph:
    - text: 90% coverage
    - link "of alert types in all supported languages with Copilot Autofix":
      - /url: https://docs.github.com/en/code-security/code-scanning/managing-your-code-scanning-configuration/codeql-query-suites
  - heading "Work together, achieve more" [level=2]
  - paragraph: Collaborate with your teams, use management tools that sync with your projects, and code from anywhere—all on a single, integrated platform.
  - img "A project management dashboard showing tasks for the ‘OctoArcade Invaders’ project, with tasks grouped under project phase categories like ‘Prototype,’ ‘Beta,’ and ‘Launch’ in a table layout. One of the columns displays sub-issue progress bars with percentages for each issue."
  - heading "Your workflows, your way. Plan effectively with an adaptable spreadsheet that syncs with your work." [level=3]
  - link "Jump into GitHub Projects":
    - /url: /features/issues
  - figure:
    - blockquote: It helps us onboard new software engineers and get them productive right away. We have all our source code, issues, and pull requests in one place... GitHub is a complete platform that frees us from menial tasks and enables us to do our best work.
    - text: Fabian Faulhaber Application manager at Mercedes-Benz
  - term:
    - button "Keep track of your tasks" [expanded]:
      - heading "Keep track of your tasks" [level=3]
  - definition:
    - paragraph: Create issues and manage projects with tools that adapt to your code.
    - text: Display of task tracking within an issue, showing the status of related sub-issues and their connection to the main issue.
    - link "Explore GitHub Issues":
      - /url: /features/issues
  - term:
    - button "Share ideas and ask questions":
      - heading "Share ideas and ask questions" [level=3]
  - term:
    - button "Review code changes together":
      - heading "Review code changes together" [level=3]
  - term:
    - button "Fund open source projects":
      - heading "Fund open source projects" [level=3]
  - heading "From startups to enterprises, GitHub scales with teams of any size in any industry." [level=2]
  - tablist:
    - tab "By industry"
    - tab "By size"
    - tab "By use case"
  - separator
  - link "Figma Technology Figma streamlines development and strengthens security Read customer story":
    - /url: https://github.com/customer-stories/figma
    - img "Figma"
    - text: Technology
    - paragraph: Figma streamlines development and strengthens security
    - text: Read customer story
  - link "Mercedes-Benz Automotive Mercedes-Benz standardizes source code and automates onboarding Read customer story":
    - /url: https://github.com/customer-stories/mercedes-benz
    - img "Mercedes-Benz"
    - text: Automotive
    - paragraph: Mercedes-Benz standardizes source code and automates onboarding
    - text: Read customer story
  - link "Mercado Libre Financial services Mercado Libre cuts coding time by 50% Read customer story":
    - /url: https://github.com/customer-stories/mercado-libre
    - img "Mercado Libre"
    - text: Financial services
    - paragraph: Mercado Libre cuts coding time by 50%
    - text: Read customer story
  - link "Explore customer stories":
    - /url: /customer-stories
  - separator
  - link "View all solutions":
    - /url: /solutions
  - heading "Millions of developers and businesses call GitHub home" [level=2]
  - paragraph: Whether you’re scaling your development process or just learning how to code, GitHub is where you belong. Join the world’s most widely adopted AI-powered developer platform to build the technologies that redefine what’s possible.
  - form "Sign up for GitHub":
    - text: Enter your email
    - textbox "Enter your email"
    - button "Sign up for GitHub"
  - link "Try GitHub Copilot":
    - /url: /github-copilot/pro?cft=copilot_lo.copilot_plans.cfi
  - heading "Footnotes" [level=2]
  - list:
    - listitem:
      - paragraph:
        - 'link "Survey: The AI wave continues to grow on software development teams, 2024."':
          - /url: https://github.blog/news-insights/research/survey-ai-wave-grows/
        - link "Back to content":
          - /url: "#footnote-ref-1"
    - listitem:
      - paragraph:
        - text: This 7X times factor is based on data from the industry’s longest running analysis of fix rates Veracode State of Software Security 2023, which cites the average time to fix 50% of flaws as 198 days vs. GitHub’s fix rates of 72% of flaws with in 28 days which is at a minimum of 7X faster when compared.
        - link "Back to content":
          - /url: "#footnote-ref-2"
  - link "Back to top":
    - /url: "#hero"
- contentinfo:
  - heading "Site-wide Links" [level=2]
  - link "Go to GitHub homepage":
    - /url: /
    - img
  - heading "Subscribe to our developer newsletter" [level=3]
  - paragraph: Get tips, technical guides, and best practices. Twice a month.
  - link "Subscribe":
    - /url: https://resources.github.com/newsletter/
  - navigation "Product":
    - heading "Product" [level=3]
    - list:
      - listitem:
        - link "Features":
          - /url: /features
      - listitem:
        - link "Enterprise":
          - /url: /enterprise
      - listitem:
        - link "Copilot":
          - /url: /features/copilot
      - listitem:
        - link "AI":
          - /url: /features/ai
      - listitem:
        - link "Security":
          - /url: /security
      - listitem:
        - link "Pricing":
          - /url: /pricing
      - listitem:
        - link "Team":
          - /url: /team
      - listitem:
        - link "Resources":
          - /url: https://resources.github.com
      - listitem:
        - link "Roadmap":
          - /url: https://github.com/github/roadmap
      - listitem:
        - link "Compare GitHub":
          - /url: https://resources.github.com/devops/tools/compare
  - navigation "Platform":
    - heading "Platform" [level=3]
    - list:
      - listitem:
        - link "Developer API":
          - /url: https://docs.github.com/get-started/exploring-integrations/about-building-integrations
      - listitem:
        - link "Partners":
          - /url: https://partner.github.com
      - listitem:
        - link "Education":
          - /url: https://github.com/edu
      - listitem:
        - link "GitHub CLI":
          - /url: https://cli.github.com
      - listitem:
        - link "GitHub Desktop":
          - /url: https://desktop.github.com
      - listitem:
        - link "GitHub Mobile":
          - /url: https://github.com/mobile
  - navigation "Support":
    - heading "Support" [level=3]
    - list:
      - listitem:
        - link "Docs":
          - /url: https://docs.github.com
      - listitem:
        - link "Community Forum":
          - /url: https://github.community
      - listitem:
        - link "Professional Services":
          - /url: https://services.github.com
      - listitem:
        - link "Premium Support":
          - /url: /enterprise/premium-support
      - listitem:
        - link "Skills":
          - /url: https://skills.github.com
      - listitem:
        - link "Status":
          - /url: https://www.githubstatus.com
      - listitem:
        - link "Contact GitHub":
          - /url: https://support.github.com?tags=dotcom-footer
  - navigation "Company":
    - heading "Company" [level=3]
    - list:
      - listitem:
        - link "About":
          - /url: https://github.com/about
      - listitem:
        - link "Why GitHub":
          - /url: https://github.com/why-github
      - listitem:
        - link "Customer stories":
          - /url: /customer-stories?type=enterprise
      - listitem:
        - link "Blog":
          - /url: https://github.blog
      - listitem:
        - link "The ReadME Project":
          - /url: /readme
      - listitem:
        - link "Careers":
          - /url: https://github.careers
      - listitem:
        - link "Newsroom":
          - /url: /newsroom
      - listitem:
        - link "Inclusion":
          - /url: /about/diversity
      - listitem:
        - link "Social Impact":
          - /url: https://socialimpact.github.com
      - listitem:
        - link "Shop":
          - /url: https://shop.github.com
  - navigation "Legal and Resource Links":
    - list:
      - listitem:
        - text: ©
        - time: "2025"
        - text: GitHub, Inc.
      - listitem:
        - link "Terms":
          - /url: https://docs.github.com/site-policy/github-terms/github-terms-of-service
      - listitem:
        - link "Privacy":
          - /url: https://docs.github.com/site-policy/privacy-policies/github-privacy-statement
        - text: (
        - link "Updated 02/2024":
          - /url: https://github.com/github/site-policy/pull/582
          - text: Updated
          - time: 02/2024
        - text: )
      - listitem:
        - link "Sitemap":
          - /url: /sitemap
      - listitem:
        - link "What is Git?":
          - /url: /git-guides
      - listitem:
        - button "Manage cookies"
      - listitem:
        - button "Do not share my personal information"
  - navigation "GitHub's Social Media Links":
    - list:
      - listitem:
        - link "GitHub on LinkedIn":
          - /url: https://www.linkedin.com/company/github
      - listitem:
        - link "GitHub on Instagram":
          - /url: https://www.instagram.com/github
      - listitem:
        - link "GitHub on YouTube":
          - /url: https://www.youtube.com/github
      - listitem:
        - link "GitHub on X":
          - /url: https://x.com/github
      - listitem:
        - link "GitHub on TikTok":
          - /url: https://www.tiktok.com/@github
      - listitem:
        - link "GitHub on Twitch":
          - /url: https://www.twitch.tv/github
      - listitem:
        - link "GitHub’s organization on GitHub":
          - /url: https://github.com/github
```

# Test source

```ts
   1 | // @ts-check
   2 | const { test, expect } = require('@playwright/test');
   3 |
   4 | test.describe('示例测试套件', () => {
   5 |   test('基本页面导航测试', async ({ page }) => {
   6 |     // 访问百度首页
   7 |     await page.goto('https://www.baidu.com');
   8 |     
   9 |     // 验证页面标题
   10 |     await expect(page).toHaveTitle(/百度/);
   11 |     
   12 |     // 验证搜索框存在
   13 |     const searchBox = page.locator('#kw');
   14 |     await expect(searchBox).toBeVisible();
   15 |     
   16 |     // 输入搜索内容
   17 |     await searchBox.fill('Playwright 自动化测试');
   18 |     
   19 |     // 点击搜索按钮
   20 |     await page.click('#su');
   21 |     
   22 |     // 等待搜索结果页面加载
   23 |     await page.waitForLoadState('networkidle');
   24 |     
   25 |     // 验证搜索结果页面
   26 |     await expect(page.locator('#content_left')).toBeVisible();
   27 |     
   28 |     console.log('✅ 百度搜索测试完成');
   29 |   });
   30 |
   31 |   test('GitHub 首页测试', async ({ page }) => {
   32 |     // 访问GitHub首页
>  33 |     await page.goto('https://github.com');
      |                ^ Error: page.goto: Test timeout of 30000ms exceeded.
   34 |     
   35 |     // 验证页面标题包含GitHub
   36 |     await expect(page).toHaveTitle(/GitHub/);
   37 |     
   38 |     // 验证登录按钮存在
   39 |     const signInButton = page.locator('a[href="/login"]').first();
   40 |     await expect(signInButton).toBeVisible();
   41 |     
   42 |     // 验证搜索框存在
   43 |     const searchButton = page.locator('[data-target="qbsearch-input.inputButton"]').first();
   44 |     await expect(searchButton).toBeVisible();
   45 |     
   46 |     console.log('✅ GitHub 首页测试完成');
   47 |   });
   48 |
   49 |   test('表单交互测试', async ({ page }) => {
   50 |     // 创建一个简单的HTML页面进行测试
   51 |     const htmlContent = `
   52 |       <!DOCTYPE html>
   53 |       <html>
   54 |       <head>
   55 |         <title>测试表单</title>
   56 |         <meta charset="utf-8">
   57 |       </head>
   58 |       <body>
   59 |         <h1>测试表单</h1>
   60 |         <form id="testForm">
   61 |           <input type="text" id="name" placeholder="姓名" required>
   62 |           <input type="email" id="email" placeholder="邮箱" required>
   63 |           <select id="city">
   64 |             <option value="">选择城市</option>
   65 |             <option value="beijing">北京</option>
   66 |             <option value="shanghai">上海</option>
   67 |             <option value="guangzhou">广州</option>
   68 |           </select>
   69 |           <input type="checkbox" id="agree"> 同意条款
   70 |           <button type="submit">提交</button>
   71 |         </form>
   72 |         <div id="result"></div>
   73 |         <script>
   74 |           document.getElementById('testForm').addEventListener('submit', function(e) {
   75 |             e.preventDefault();
   76 |             document.getElementById('result').textContent = '表单提交成功！';
   77 |           });
   78 |         </script>
   79 |       </body>
   80 |       </html>
   81 |     `;
   82 |     
   83 |     // 使用data URL加载HTML内容
   84 |     await page.goto(`data:text/html,${encodeURIComponent(htmlContent)}`);
   85 |     
   86 |     // 填写表单
   87 |     await page.fill('#name', '张三');
   88 |     await page.fill('#email', '<EMAIL>');
   89 |     await page.selectOption('#city', 'beijing');
   90 |     await page.check('#agree');
   91 |     
   92 |     // 提交表单
   93 |     await page.click('button[type="submit"]');
   94 |     
   95 |     // 验证结果
   96 |     await expect(page.locator('#result')).toHaveText('表单提交成功！');
   97 |     
   98 |     console.log('✅ 表单交互测试完成');
   99 |   });
  100 | });
  101 |
```