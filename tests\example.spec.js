// @ts-check
const { test, expect } = require('@playwright/test');

test.describe('示例测试套件', () => {
  test('基本页面导航测试', async ({ page }) => {
    // 访问百度首页
    await page.goto('https://www.baidu.com');
    
    // 验证页面标题
    await expect(page).toHaveTitle(/百度/);
    
    // 验证搜索框存在
    const searchBox = page.locator('#kw');
    await expect(searchBox).toBeVisible();
    
    // 输入搜索内容
    await searchBox.fill('Playwright 自动化测试');
    
    // 点击搜索按钮
    await page.click('#su');
    
    // 等待搜索结果页面加载
    await page.waitForLoadState('networkidle');
    
    // 验证搜索结果页面
    await expect(page.locator('#content_left')).toBeVisible();
    
    console.log('✅ 百度搜索测试完成');
  });

  test('GitHub 首页测试', async ({ page }) => {
    // 访问GitHub首页
    await page.goto('https://github.com');
    
    // 验证页面标题包含GitHub
    await expect(page).toHaveTitle(/GitHub/);
    
    // 验证登录按钮存在
    const signInButton = page.locator('a[href="/login"]').first();
    await expect(signInButton).toBeVisible();
    
    // 验证搜索框存在
    const searchButton = page.locator('[data-target="qbsearch-input.inputButton"]').first();
    await expect(searchButton).toBeVisible();
    
    console.log('✅ GitHub 首页测试完成');
  });

  test('表单交互测试', async ({ page }) => {
    // 创建一个简单的HTML页面进行测试
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>测试表单</title>
        <meta charset="utf-8">
      </head>
      <body>
        <h1>测试表单</h1>
        <form id="testForm">
          <input type="text" id="name" placeholder="姓名" required>
          <input type="email" id="email" placeholder="邮箱" required>
          <select id="city">
            <option value="">选择城市</option>
            <option value="beijing">北京</option>
            <option value="shanghai">上海</option>
            <option value="guangzhou">广州</option>
          </select>
          <input type="checkbox" id="agree"> 同意条款
          <button type="submit">提交</button>
        </form>
        <div id="result"></div>
        <script>
          document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            document.getElementById('result').textContent = '表单提交成功！';
          });
        </script>
      </body>
      </html>
    `;
    
    // 使用data URL加载HTML内容
    await page.goto(`data:text/html,${encodeURIComponent(htmlContent)}`);
    
    // 填写表单
    await page.fill('#name', '张三');
    await page.fill('#email', '<EMAIL>');
    await page.selectOption('#city', 'beijing');
    await page.check('#agree');
    
    // 提交表单
    await page.click('button[type="submit"]');
    
    // 验证结果
    await expect(page.locator('#result')).toHaveText('表单提交成功！');
    
    console.log('✅ 表单交互测试完成');
  });
});
